# -*- mode: python ; coding: utf-8 -*-
import os
from PyInstaller.utils.hooks import collect_data_files

block_cipher = None

# 获取当前目录的绝对路径
current_dir = os.path.abspath(os.getcwd())
dist_path = os.path.join(current_dir, 'dist', '七彩虹托运单处理工具')

a = Analysis(
    ['托运单GUI.py'],
    pathex=[current_dir],
    binaries=[],
    datas=[
        ('logo.png', '.'),
        ('安捷通托运书.xlsx', '.'),
        ('副本托运单.xlsx', '.')
    ],
    hiddenimports=[
        'pandas',
        'openpyxl',
        'PyQt5',
        'numpy',
        'pypinyin',
        'xlrd',
        'xlwt'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='七彩虹托运单处理工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.png'
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='七彩虹托运单处理工具'
)